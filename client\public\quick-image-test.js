/**
 * Quick Image Display Test for Emma Studio
 * Run this in the browser console on the Visual Complexity Analyzer page
 * 
 * Usage: Copy and paste this entire script into the browser console and press Enter
 */

(async function quickImageDisplayTest() {
  console.log('🚀 Starting Quick Image Display Test...');
  
  try {
    // Step 1: Check if we're on the right page
    if (!window.location.pathname.includes('visual-complexity') && !window.location.pathname.includes('design-complexity')) {
      console.log('⚠️ Navigate to the Visual Complexity Analyzer page first');
      return;
    }
    
    // Step 2: Import required modules
    console.log('📦 Importing modules...');
    const { supabase } = await import('/src/lib/supabase.ts');
    const { designAnalysisService } = await import('/src/services/designAnalysisService.ts');
    
    console.log('✅ Modules imported successfully');
    
    // Step 3: Check authentication
    console.log('🔐 Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ Authentication failed:', authError?.message || 'No user found');
      console.log('💡 Please log in and try again');
      return;
    }
    
    console.log('✅ User authenticated:', user.email);
    
    // Step 4: Test storage access
    console.log('📦 Testing storage access...');
    const { data: files, error: listError } = await supabase.storage
      .from('design-analysis-images')
      .list(user.id, { limit: 3 });
    
    if (listError) {
      console.log('❌ Storage access failed:', listError.message);
      console.log('💡 Check RLS policies and bucket permissions');
      return;
    }
    
    console.log('✅ Storage accessible, found', files?.length || 0, 'files');
    
    if (!files || files.length === 0) {
      console.log('📝 No files found. Upload an image in the Visual Complexity Analyzer first.');
      return;
    }
    
    // Step 5: Test image retrieval
    console.log('🖼️ Testing image retrieval...');
    const testFile = files[0];
    const filePath = `${user.id}/${testFile.name}`;
    
    console.log('🧪 Testing file:', testFile.name);
    
    // Test direct download
    console.log('📥 Testing direct download...');
    const { data: fileBlob, error: downloadError } = await supabase.storage
      .from('design-analysis-images')
      .download(filePath);
    
    if (downloadError) {
      console.log('❌ Direct download failed:', downloadError.message);
      return;
    }
    
    console.log('✅ Direct download successful:', {
      size: fileBlob.size,
      type: fileBlob.type
    });
    
    // Test object URL creation
    console.log('🔗 Creating object URL...');
    const objectUrl = URL.createObjectURL(fileBlob);
    console.log('✅ Object URL created:', objectUrl.substring(0, 50) + '...');
    
    // Test image loading
    console.log('🧪 Testing image loading...');
    const loadTest = await new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        console.log('✅ Image loads successfully!');
        resolve(true);
      };
      img.onerror = (error) => {
        console.log('❌ Image failed to load:', error);
        resolve(false);
      };
      img.src = objectUrl;
      
      // Timeout after 5 seconds
      setTimeout(() => {
        console.log('⏰ Image load timeout');
        resolve(false);
      }, 5000);
    });
    
    // Clean up
    URL.revokeObjectURL(objectUrl);
    
    // Step 6: Test service method
    console.log('🔧 Testing service method...');
    const serviceUrl = await designAnalysisService.getImageUrl(filePath);
    
    if (serviceUrl) {
      console.log('✅ Service method successful:', {
        type: serviceUrl.startsWith('blob:') ? 'Blob URL' : 'HTTP URL',
        url: serviceUrl.substring(0, 50) + '...'
      });
      
      // Test service URL loading
      const serviceLoadTest = await new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = serviceUrl;
        setTimeout(() => resolve(false), 5000);
      });
      
      console.log('🧪 Service URL test:', serviceLoadTest ? '✅ Success' : '❌ Failed');
      
      // Clean up service URL if it's a blob
      if (serviceUrl.startsWith('blob:')) {
        URL.revokeObjectURL(serviceUrl);
      }
    } else {
      console.log('❌ Service method failed');
    }
    
    // Step 7: Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('================');
    console.log('✅ Authentication: Working');
    console.log('✅ Storage Access: Working');
    console.log('✅ File Download: Working');
    console.log('✅ Object URL Creation: Working');
    console.log(loadTest ? '✅ Image Loading: Working' : '❌ Image Loading: Failed');
    console.log(serviceUrl ? '✅ Service Method: Working' : '❌ Service Method: Failed');
    
    if (loadTest && serviceUrl) {
      console.log('\n🎉 ALL TESTS PASSED! Image display should be working correctly.');
      console.log('💡 If you\'re still seeing issues, try:');
      console.log('   1. Refresh the page');
      console.log('   2. Clear browser cache');
      console.log('   3. Check browser console for other errors');
    } else {
      console.log('\n⚠️ Some tests failed. This indicates an issue with image display.');
      console.log('💡 Try these troubleshooting steps:');
      console.log('   1. Check your internet connection');
      console.log('   2. Refresh the page and try again');
      console.log('   3. Clear browser cache and cookies');
      console.log('   4. Try uploading a new image');
    }
    
  } catch (error) {
    console.log('💥 Test failed with error:', error.message);
    console.log('🔍 Full error:', error);
  }
})();

console.log('🎯 Quick Image Display Test loaded and running...');
